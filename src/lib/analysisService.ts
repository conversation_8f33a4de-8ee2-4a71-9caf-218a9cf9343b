import fs from 'fs/promises';
import path from 'path';
import { IAnalyzedReport, IReporterStats, IOverallAnalysis } from '../types/analysisTypes';
import { ParsedRawIssue, IParsedRawIssue } from './parser/core_parser'; // Import ParsedRawIssue class and IParsedRawIssue interface

const SNAPSHOT_DIR = process.env.SNAPSHOT_DIR;
const CACHE_DIR = path.join(process.cwd(), 'analysis_cache');
const CACHE_FILE_PATH = path.join(CACHE_DIR, 'analysis_results.json');

const CLUSTERFUZZ_REPORTER_SUFFIX = '@project.gserviceaccount.com'; // More general suffix
const CLUSTERFUZZ_REPORTER_EXACT = '<EMAIL>';

async function ensureCacheDirExists(): Promise<void> {
  try {
    await fs.mkdir(CACHE_DIR, { recursive: true });
  } catch (error) {
    console.error('Failed to create cache directory:', error);
    // Depending on desired behavior, you might want to throw this error
  }
}

// Helper to safely get string or undefined
const safeString = (value: any): string | undefined => typeof value === 'string' ? value : undefined;
// Helper to safely get number or undefined
const safeNumber = (value: any): number | undefined => typeof value === 'number' ? value : undefined;
// Helper to safely get string array or undefined, then join
const safeComponentPath = (value: string[] | undefined): string | undefined => Array.isArray(value) ? value.join('>') : undefined;

async function performAnalysisAndCache(): Promise<IOverallAnalysis> {
  if (!SNAPSHOT_DIR) {
    throw new Error('SNAPSHOT_DIR environment variable is not set.');
  }
  await ensureCacheDirExists();

  const analyzedReports: IAnalyzedReport[] = [];
  const reporterData: { [email: string]: { totalReward: number; reportCount: number; maxReward: number; reportIds: string[] } } = {};

  try {
    const reportDirs = await fs.readdir(SNAPSHOT_DIR);

    for (const reportId of reportDirs) {
      const reportPath = path.join(SNAPSHOT_DIR, reportId);
      const stat = await fs.stat(reportPath);

      if (stat.isDirectory()) {
        const coreJsonPath = path.join(reportPath, 'core.json');
        try {
          const coreJsonContent = await fs.readFile(coreJsonPath, 'utf-8');
          // Convert reportId to number for issueNumber, assuming reportId is numeric
          // It's better if core.json itself contains the true issue ID that ParsedRawIssue can use.
          // For now, we'll use reportId as a fallback if direct parsing of issue ID from rawData is not done by ParsedRawIssue constructor.
          const numericReportId = parseInt(reportId, 10);
          if (isNaN(numericReportId)) {
            console.warn(`Report ID ${reportId} is not a valid number. Skipping.`);
            continue;
          }
          const parsedData: IParsedRawIssue = new ParsedRawIssue(numericReportId, JSON.parse(coreJsonContent));

          const issueId = safeNumber(parsedData.issueNumber); // Use issueNumber
          const title = safeString(parsedData.title); // Directly from IParsedRawIssue
          const reporter = safeString(parsedData.reporter); // Directly from IParsedRawIssue
          
          let disclosedTime: string | undefined;
          // Use timestamp of the first comment as a proxy for disclosed time
          // This might need refinement based on where 'published_time' for the issue is actually stored
          if (parsedData.comments && parsedData.comments.length > 0 && parsedData.comments[0].timestamp) {
            disclosedTime = new Date(parsedData.comments[0].timestamp * 1000).toISOString();
          }

          let rewardAmount: number | undefined;
          if (parsedData.reward_amount !== null) {
            rewardAmount = parsedData.reward_amount;
          }

          const componentPath = safeComponentPath(parsedData.componentPaths); // Use componentPaths (plural)

          const isClusterfuzz = reporter === CLUSTERFUZZ_REPORTER_EXACT || (reporter?.endsWith(CLUSTERFUZZ_REPORTER_SUFFIX) && reporter?.startsWith('24'));
          const isV8 = title?.toLowerCase().includes('v8') || componentPath?.toLowerCase().includes('javascript') || false;
          const isWebGPU = title?.toLowerCase().includes('webgpu') || false;

          const analyzedReport: IAnalyzedReport = {
            reportId,
            issueId,
            title,
            reporter,
            disclosedTime,
            rewardAmount,
            componentPath,
            isClusterfuzz,
            isV8,
            isWebGPU,
          };
          analyzedReports.push(analyzedReport);

          if (reporter) {
            if (!reporterData[reporter]) {
              reporterData[reporter] = { totalReward: 0, reportCount: 0, maxReward: 0, reportIds: [] };
            }
            reporterData[reporter].reportCount++;
            reporterData[reporter].reportIds.push(reportId);
            if (rewardAmount) {
              reporterData[reporter].totalReward += rewardAmount;
              if (rewardAmount > reporterData[reporter].maxReward) {
                reporterData[reporter].maxReward = rewardAmount;
              }
            }
          }
        } catch (err) {
          console.warn(`Skipping report ${reportId} due to error reading/parsing core.json:`, err);
        }
      }
    }
  } catch (err) {
    console.error('Error reading snapshot directory:', err);
    throw new Error('Failed to analyze reports from snapshot directory.');
  }

  const reporterStats: IReporterStats[] = Object.entries(reporterData).map(([email, stats]) => ({
    reporter: email,
    totalReports: stats.reportCount,
    totalReward: stats.totalReward,
    maxSingleReward: stats.maxReward,
    reportIds: stats.reportIds,
  }));

  // Sort reports by disclosedTime in descending order (newest first)
  const sortedReports = [...analyzedReports].sort((a, b) => {
    const timeA = a.disclosedTime ? new Date(a.disclosedTime).getTime() : 0;
    const timeB = b.disclosedTime ? new Date(b.disclosedTime).getTime() : 0;
    return timeB - timeA; // Sort newest first
  });

  const overallAnalysis: IOverallAnalysis = {
    reports: sortedReports,
    reporterStats,
    lastRefreshed: new Date().toISOString(),
  };

  try {
    await fs.writeFile(CACHE_FILE_PATH, JSON.stringify(overallAnalysis, null, 2));
  } catch (err) {
    console.error('Failed to write analysis cache file:', err);
    // Decide if this should throw or just log
  }

  return overallAnalysis;
}

export async function getAnalysisResults(forceRefresh: boolean = false): Promise<IOverallAnalysis | null> {
  if (!SNAPSHOT_DIR) {
    console.error('SNAPSHOT_DIR environment variable is not set.');
    return { reports: [], reporterStats: [], lastRefreshed: new Date().toISOString(), error: 'SNAPSHOT_DIR not configured.' };
  }

  if (forceRefresh) {
    try {
      return await performAnalysisAndCache();
    } catch (error: any) {
      console.error('Error performing forced analysis:', error);
      return { reports: [], reporterStats: [], lastRefreshed: new Date().toISOString(), error: error.message || 'Forced analysis failed.' };
    }
  }

  try {
    await fs.access(CACHE_FILE_PATH);
    const cachedData = await fs.readFile(CACHE_FILE_PATH, 'utf-8');
    return JSON.parse(cachedData) as IOverallAnalysis;
  } catch (error) {
    // Cache miss or error reading cache, perform fresh analysis
    console.log('Cache miss or error reading cache. Performing fresh analysis.');
    try {
      return await performAnalysisAndCache();
    } catch (analysisError: any) {
      console.error('Error performing initial analysis:', analysisError);
      return { reports: [], reporterStats: [], lastRefreshed: new Date().toISOString(), error: analysisError.message || 'Initial analysis failed.' };
    }
  }
}
