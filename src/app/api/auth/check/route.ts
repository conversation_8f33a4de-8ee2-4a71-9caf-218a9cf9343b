import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';

const COOKIE_NAME = 'app_pin_verified';

export async function GET(request: NextRequest) {
  try {
    const isAuthenticated = cookies().get(COOKIE_NAME)?.value === 'true';
    
    if (isAuthenticated) {
      return NextResponse.json({ authenticated: true }, { status: 200 });
    } else {
      return NextResponse.json({ authenticated: false }, { status: 401 });
    }
  } catch (error) {
    console.error('Auth check API error:', error);
    return NextResponse.json({ message: 'An unexpected error occurred during authentication check.' }, { status: 500 });
  }
} 