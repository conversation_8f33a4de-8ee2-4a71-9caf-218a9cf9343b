'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import { IAnalyzedReport } from '../../types/analysisTypes';
import FilterModule from './components/FilterModule';

interface IOverallAnalysis {
  reports: IAnalyzedReport[];
  lastRefreshed?: string;
  error?: string;
  reporterStats?: any;
}

interface ReportListPageProps {
  title: string;
}

export default function ReportListPage({ title }: ReportListPageProps) {
  const [analysisResult, setAnalysisResult] = useState<IOverallAnalysis | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filteredReports, setFilteredReports] = useState<IAnalyzedReport[]>([]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/analysis');
        if (!response.ok) {
          throw new Error('Failed to fetch analysis data');
        }
        const data: IOverallAnalysis = await response.json();
        setAnalysisResult(data);
        setFilteredReports(data.reports || []);
      } catch (err: any) {
        setError(err.message || 'An error occurred while fetching data');
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleFilterChange = (filteredReports: IAnalyzedReport[]) => {
    setFilteredReports(filteredReports);
  };

  if (isLoading) return <div className="p-4">Loading...</div>;
  if (error) return <div className="p-4 text-red-500">Error: {error}</div>;
  if (!analysisResult) return <div className="p-4">No data available</div>;

  return (
    <div className="container mx-auto p-4">
      <div className="flex flex-col gap-4 mb-6">
        <h1 className="text-2xl font-bold">{title}</h1>
      </div>

      {/* Include the FilterModule component */}
      {analysisResult.reports && (
        <FilterModule 
          reports={analysisResult.reports} 
          onFilterChange={handleFilterChange} 
        />
      )}

      {filteredReports.length === 0 ? (
        <div className="p-4 bg-slate-800/50 rounded-lg">
          <p className="text-slate-400">No reports found matching the current filters.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredReports.map((report) => (
            <Link 
              key={report.reportId} 
              href={`/reports/${report.reportId}`}
              className="block bg-slate-700/50 rounded-md shadow p-4 hover:bg-slate-600/50 transition-colors duration-150"
            >
              <h2 className="text-md font-semibold text-sky-400 truncate" title={report.title || 'N/A'}>
                {report.title || `Report ${report.reportId}`}
              </h2>
              <p className="text-xs text-slate-300 mt-1">
                ID: <span className="font-mono">{report.issueId || report.reportId}</span>
              </p>
              <p className="text-xs text-slate-400 mt-1">
                Reporter: <span className="font-medium text-slate-300" title={report.reporter || 'N/A'}>
                  {report.reporter || 'N/A'}
                </span>
              </p>
              <p className="text-xs text-slate-400 mt-1">
                Reward: <span className="font-semibold text-green-400">
                  {typeof report.rewardAmount === 'number' ? `$${report.rewardAmount.toLocaleString()}` : 'N/A'}
                </span>
              </p>
              {report.componentPath && (
                <p className="text-xs text-slate-400 mt-1">
                  Component: <span className="font-medium text-slate-300">
                    {report.componentPath}
                  </span>
                </p>
              )}
            </Link>
          ))}
        </div>
      )}

      <div className="mt-8 text-center">
        <Link 
          href="/" 
          className="inline-flex items-center text-sky-400 hover:text-sky-300 transition-colors"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          Back to Dashboard
        </Link>
      </div>
    </div>
  );
}
