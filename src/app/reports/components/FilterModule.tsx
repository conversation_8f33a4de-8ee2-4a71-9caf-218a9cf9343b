import { useState, useEffect } from 'react';
import { IAnalyzedReport } from '../../../types/analysisTypes';

export interface FilterTag {
  id: string;
  name: string;
  filters: FilterOptions;
}

export interface FilterOptions {
  titleKeyword: string;
  reporter: string;
  minReward: number | null;
  maxReward: number | null;
  componentPath: string;
  componentTags: string[];
  isClusterfuzz?: boolean;
  isV8?: boolean;
  isWebGPU?: boolean;
}

interface FilterModuleProps {
  reports: IAnalyzedReport[];
  onFilterChange: (filteredReports: IAnalyzedReport[]) => void;
}

// Default tags for common filters
const DEFAULT_TAGS: FilterTag[] = [
  {
    id: 'default-clusterfuzz',
    name: 'Clusterfuzz',
    filters: {
      titleKeyword: '',
      reporter: '',
      minReward: null,
      maxReward: null,
      componentPath: '',
      componentTags: [],
      isClusterfuzz: true
    }
  },
  {
    id: 'default-v8',
    name: 'V8',
    filters: {
      titleKeyword: '',
      reporter: '',
      minReward: null,
      maxReward: null,
      componentPath: '',
      componentTags: [],
      isV8: true
    }
  },
  {
    id: 'default-webgpu',
    name: 'WebGPU',
    filters: {
      titleKeyword: '',
      reporter: '',
      minReward: null,
      maxReward: null,
      componentPath: '',
      componentTags: [],
      isWebGPU: true
    }
  },
  {
    id: 'default-rewarded',
    name: 'Rewarded Reports',
    filters: {
      titleKeyword: '',
      reporter: '',
      minReward: 1, // At least $1 reward
      maxReward: null,
      componentPath: '',
      componentTags: []
    }
  }
];

export default function FilterModule({ reports, onFilterChange }: FilterModuleProps) {
  const [filters, setFilters] = useState<FilterOptions>({
    titleKeyword: '',
    reporter: '',
    minReward: null,
    maxReward: null,
    componentPath: '',
    componentTags: [],
  });
  
  const [savedTags, setSavedTags] = useState<FilterTag[]>([]);
  const [tagName, setTagName] = useState('');
  const [showSaveTag, setShowSaveTag] = useState(false);

  // Load saved tags from localStorage on component mount
  useEffect(() => {
    const savedTagsJson = localStorage.getItem('filterTags');
    if (savedTagsJson) {
      try {
        setSavedTags(JSON.parse(savedTagsJson));
      } catch (e) {
        console.error('Failed to parse saved filter tags:', e);
        // If there's an error parsing, initialize with default tags
        setSavedTags(DEFAULT_TAGS);
      }
    } else {
      // If no saved tags, initialize with default tags
      setSavedTags(DEFAULT_TAGS);
    }
  }, []);

  // Save tags to localStorage whenever they change
  useEffect(() => {
    // Only save user-created tags, not default tags
    const userTags = savedTags.filter(tag => !tag.id.startsWith('default-'));
    localStorage.setItem('filterTags', JSON.stringify(userTags));
  }, [savedTags]);

  // Apply filters to reports whenever filters change
  useEffect(() => {
    const filteredReports = reports.filter(report => {
      // Title keyword filter
      if (filters.titleKeyword && report.title) {
        if (!report.title.toLowerCase().includes(filters.titleKeyword.toLowerCase())) {
          return false;
        }
      }

      // Reporter filter
      if (filters.reporter && report.reporter) {
        if (!report.reporter.toLowerCase().includes(filters.reporter.toLowerCase())) {
          return false;
        }
      }

      // Reward amount filter
      if (filters.minReward !== null && report.rewardAmount !== undefined) {
        if (report.rewardAmount < filters.minReward) {
          return false;
        }
      }
      
      if (filters.maxReward !== null && report.rewardAmount !== undefined) {
        if (report.rewardAmount > filters.maxReward) {
          return false;
        }
      }

      // Component path filter
      if (filters.componentPath && report.componentPath) {
        if (!report.componentPath.includes(filters.componentPath)) {
          return false;
        }
      }

      // Special filters for categories
      if (filters.isClusterfuzz && !report.isClusterfuzz) {
        return false;
      }
      
      if (filters.isV8 && !report.isV8) {
        return false;
      }
      
      if (filters.isWebGPU && !report.isWebGPU) {
        return false;
      }

      return true;
    });

    onFilterChange(filteredReports);
  }, [filters, reports, onFilterChange]);

  // Handler for saving current filters as a tag
  const saveAsTag = () => {
    if (!tagName.trim()) return;
    
    const newTag: FilterTag = {
      id: Date.now().toString(),
      name: tagName.trim(),
      filters: { ...filters }
    };
    
    setSavedTags([...savedTags, newTag]);
    setTagName('');
    setShowSaveTag(false);
  };

  // Handler for applying a saved tag
  const applyTag = (tag: FilterTag) => {
    setFilters(tag.filters);
  };

  // Handler for deleting a saved tag
  const deleteTag = (tagId: string) => {
    // Don't allow deletion of default tags
    if (tagId.startsWith('default-')) return;
    
    setSavedTags(savedTags.filter(tag => tag.id !== tagId));
  };

  // Handler for clearing all filters
  const clearFilters = () => {
    setFilters({
      titleKeyword: '',
      reporter: '',
      minReward: null,
      maxReward: null,
      componentPath: '',
      componentTags: [],
    });
  };

  return (
    <div className="bg-slate-800 rounded-lg p-4 mb-6">
      <h2 className="text-lg font-semibold mb-4">Filter Reports</h2>
      
      {/* Filter inputs */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <div>
          <label className="block text-sm text-slate-400 mb-1">Title Contains</label>
          <input 
            type="text" 
            value={filters.titleKeyword}
            onChange={(e) => setFilters({...filters, titleKeyword: e.target.value})}
            className="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-sm"
            placeholder="Enter keywords..."
          />
        </div>
        
        <div>
          <label className="block text-sm text-slate-400 mb-1">Reporter</label>
          <input 
            type="text" 
            value={filters.reporter}
            onChange={(e) => setFilters({...filters, reporter: e.target.value})}
            className="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-sm"
            placeholder="Reporter name or email..."
          />
        </div>
        
        <div>
          <label className="block text-sm text-slate-400 mb-1">Min Reward ($)</label>
          <input 
            type="number" 
            value={filters.minReward || ''}
            onChange={(e) => setFilters({...filters, minReward: e.target.value ? Number(e.target.value) : null})}
            className="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-sm"
            placeholder="Minimum reward amount..."
          />
        </div>
        
        <div>
          <label className="block text-sm text-slate-400 mb-1">Max Reward ($)</label>
          <input 
            type="number" 
            value={filters.maxReward || ''}
            onChange={(e) => setFilters({...filters, maxReward: e.target.value ? Number(e.target.value) : null})}
            className="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-sm"
            placeholder="Maximum reward amount..."
          />
        </div>
        
        <div>
          <label className="block text-sm text-slate-400 mb-1">Component Path</label>
          <input 
            type="text" 
            value={filters.componentPath}
            onChange={(e) => setFilters({...filters, componentPath: e.target.value})}
            className="w-full bg-slate-700 border border-slate-600 rounded-md px-3 py-2 text-sm"
            placeholder="e.g., Blink>Accessibility"
          />
        </div>
      </div>
      
      {/* Action buttons */}
      <div className="flex flex-wrap gap-2 mb-4">
        <button 
          onClick={clearFilters}
          className="bg-slate-600 hover:bg-slate-500 text-white py-1 px-3 rounded-md text-sm"
        >
          Clear Filters
        </button>
        <button 
          onClick={() => setShowSaveTag(true)}
          className="bg-sky-600 hover:bg-sky-500 text-white py-1 px-3 rounded-md text-sm"
        >
          Save as Tag
        </button>
      </div>
      
      {/* Save tag form */}
      {showSaveTag && (
        <div className="mb-4 p-3 border border-slate-600 rounded-md bg-slate-700">
          <div className="flex gap-2 mb-2">
            <input 
              type="text" 
              value={tagName}
              onChange={(e) => setTagName(e.target.value)}
              className="flex-grow bg-slate-600 border border-slate-500 rounded-md px-3 py-1 text-sm"
              placeholder="Enter tag name..."
            />
            <button 
              onClick={saveAsTag}
              className="bg-green-600 hover:bg-green-500 text-white py-1 px-3 rounded-md text-sm"
              disabled={!tagName.trim()}
            >
              Save
            </button>
            <button 
              onClick={() => setShowSaveTag(false)}
              className="bg-slate-500 hover:bg-slate-400 text-white py-1 px-3 rounded-md text-sm"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
      
      {/* Saved tags */}
      {savedTags.length > 0 && (
        <div>
          <h3 className="text-sm font-semibold text-slate-300 mb-2">Filter Tags</h3>
          <div className="flex flex-wrap gap-2">
            {savedTags.map(tag => (
              <div 
                key={tag.id} 
                className={`flex items-center rounded-full px-3 py-1 ${
                  tag.id.startsWith('default-') 
                    ? 'bg-sky-800/50 border border-sky-700' 
                    : 'bg-slate-700'
                }`}
              >
                <button 
                  onClick={() => applyTag(tag)}
                  className="text-sm text-sky-400 hover:text-sky-300 mr-2"
                >
                  {tag.name}
                </button>
                {!tag.id.startsWith('default-') && (
                  <button 
                    onClick={() => deleteTag(tag.id)}
                    className="text-slate-400 hover:text-slate-300"
                  >
                    <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
} 