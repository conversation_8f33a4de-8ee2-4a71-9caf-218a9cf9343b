// src/app/page.tsx
'use client';

import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

export default function HomePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Check if user is authenticated
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const response = await fetch('/api/auth/check');
        if (response.ok) {
          setIsAuthenticated(true);
          // Redirect to reports page if authenticated
          router.push('/reports/all');
        } else {
          // If not authenticated, redirect to login
          router.push('/login');
        }
      } catch (error) {
        console.error('Auth check error:', error);
        router.push('/login');
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, [router]);

  return (
    <div className="flex min-h-screen flex-col items-center justify-center bg-gradient-to-br from-slate-900 to-slate-800 p-4">
      <div className="w-full max-w-md rounded-xl bg-slate-800/50 p-8 shadow-2xl backdrop-blur-lg text-center">
        <h1 className="mb-2 text-center text-3xl font-bold text-sky-400">
          {process.env.NEXT_PUBLIC_APP_NAME || 'Crbug Snapshot Dashboard'}
        </h1>
        <p className="text-slate-400">
          {isLoading ? 'Loading...' : (isAuthenticated ? 'Redirecting to reports...' : 'Redirecting to login...')}
        </p>
      </div>
    </div>
  );
}
